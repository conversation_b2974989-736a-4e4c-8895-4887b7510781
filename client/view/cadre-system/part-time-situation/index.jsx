import {
  Button,
  DatePicker,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Popconfirm,
  Radio,
  Table,
} from "antd";
import {
  addOrUpdatePartTimeCert,
  deletePartTimeCert,
  exportPartTimeCert,
  getPartTimeQuery,
} from "client/apis/cadre-portrait";
import OrgTree from "client/components/org-tree";
import moment from "moment";
import { useEffect, useRef, useState } from "react";
import PAutoComplete from "../components/PAutoComplete";
import "./index.less";
const { MonthPicker } = DatePicker;
function index({ form, history }) {
  const { TextArea } = Input;
  const { RangePicker } = DatePicker;
  const { getFieldDecorator, getFieldsValue, resetFields } = form;

  const modalFormRef = useRef(null);
  const [org_ids, setOrgIds] = useState(["1"]);
  const [org_name, setOrgName] = useState("");
  const [dataSource, setDataSource] = useState([]);
  const [visible, setVisible] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [modalType, setModalType] = useState("add");
  const [loading, setLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [pagination, setPagination] = useState({
    total: 0,
  });
  useEffect(() => {
    loadData();
  }, []);

  // 加载数据
  const loadData = async ({ page: _page = 1, org_id = org_ids[0] } = {}) => {
    const fields = getFieldsValue();
    const params = {
      page: _page,
      org_id,
      name: fields.name,
      page_size: pageSize,
      part_time_position: fields.part_time_position,
    };
    try {
      const res = await getPartTimeQuery(params);
      if (res.data.code === 0) {
        // 从 JSON 数据中直接获取 data 字段
        const { data, total } = res.data.data;
        // 处理数据，添加序号和兼职时间字段
        const processedData =
          data &&
          data.map((item, index) => ({
            ...item,
            index: index + 1, // 序号从 1 开始
            part_time: `${item.part_start_time} - ${item.part_end_time}`, // 拼接兼职时间
          }));

        setDataSource(processedData);
        setPagination((prev) => ({
          ...prev,
          total: total,
        }));
      } else {
        message.error(res.data.message);
        // 可选：在错误情况下重置数据源和分页信息
        setDataSource([]);
        setPagination((prev) => ({
          ...prev,
          total: 0,
        }));
      }
    } catch (error) {
      message.error("请求失败，请稍后再试");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };
  const onChange = (orgs, org) => {
    setPage(1);
    setOrgIds(() => orgs);
    setOrgName(org.name);
    loadData({
      page: 1,
      org_id: orgs[0],
    });
  };

  const onExport = async () => {
    const params = getFieldsValue();
    const res = await exportPartTimeCert({
      ...params,
      org_id: org_ids[0],
    });
  };

  const onAdd = () => {
    setVisible(true);
    setModalType("add");
  };
  //兼职情况导入
  const onInputFile = () => {
    history.push(`/import-page?type=21`);
  };

  const onDel = async (record) => {
    try {
      const res = await deletePartTimeCert({
        id: record.part_time_id,
      });

      if (res.data.code === 0) {
        // 删除成功
        message.success("删除成功");
        loadData(); // 重新加载数据
      } else {
        // 删除失败
        message.error(res.data.message || "删除失败，请稍后再试");
      }
    } catch (error) {
      // 网络或其他错误
      message.error("删除失败，请稍后再试");
    }
  };

  const onEditor = (record) => {
    setVisible(true);
    setModalType("edit");
    queueMicrotask(() => {
      const _ = { ...record };
      // 将 name 字段的值赋给 user_name
      _.user_name = _.name;
      delete _.name; // 如果不需要保留 name 字段，可以删除

      // 处理 part_time 字符串，拆分为两个 moment 对象
      if (_.part_time) {
        const [start, end] = _.part_time
          .split(" - ")
          .map((date) => moment(date, "YYYY.MM.DD"));
        _.part_time = [start, end]; // 设置为 RangePicker 的值
      }

      // 设置表单值
      modalFormRef.current.setFieldsValue(_);
    });
  };

  const columns = [
    {
      title: "序号",
      dataIndex: "index",
      key: "index",
      width: 50,
      align: "center",
    },
    {
      title: "姓名",
      dataIndex: "name",
      key: "name",
      width: 100,
      align: "center",
    },
    {
      title: "现(原)任职务",
      dataIndex: "current_position",
      key: "current_position",
      width: 120,
      align: "center",
    },
    {
      title: "兼任职务",
      dataIndex: "part_time_position",
      key: "part_time_position",
      width: 120,
      align: "center",
    },
    {
      title: (
        <div style={{ textAlign: "center" }}>
          所兼职社会团体
          <br />
          (企业)业务主管部门
        </div>
      ),
      dataIndex: "business_department",
      key: "business_department",
      width: 120,
      align: "center",
    },
    {
      title: "兼职时间",
      dataIndex: "part_time",
      key: "part_time",
      width: 240,
      align: "center",
    },
    {
      title: "任期届数",
      dataIndex: "term_count",
      key: "term_count",
      align: "center",
    },
    {
      title: "是否兼法人代表",
      dataIndex: "legal_representative_text",
      key: "legal_representative_text",
      width: 120,
      align: "center",
    },
    {
      title: "是否报批",
      dataIndex: "approved_text",
      key: "approved_text",
      width: 100,
      align: "center",
    },
    {
      title: "审批文号",
      dataIndex: "approval_number",
      key: "approval_number",
      width: 120,
      align: "center",
    },
    {
      title: "操作",
      dataIndex: "operation",
      key: "operation",
      width: 100,
      align: "center",
      render(_, record) {
        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              gap: "0px 10px",
            }}
          >
            <a
              onClick={() => {
                onEditor(record);
              }}
            >
              编辑
            </a>
            <Popconfirm
              title="确认删除?"
              onConfirm={() => onDel(record)}
              okText="确认"
              cancelText="取消"
            >
              <a style={{ color: "red" }}>删除</a>
            </Popconfirm>
          </div>
        );
      },
    },
  ];

  const paginationProps = {
    pageSize: 10,
    onChange: (page) => {
      setPage(page);
      loadData({ page });
    },
    current: page,
    total: pagination.total,
  };

  const onReset = () => {
    resetFields();
    onSearch();
  };

  const onSearch = () => {
    setDataSource([]); // 清空旧数据
    setPage(1);
    loadData({ page: 1 });
  };

  const modalHandleOk = () => {
    const form = modalFormRef.current;
    form.validateFields(async (err, values) => {
      const { user_name: _user_name } = values;
      if (!err) {
        if (toString.call(_user_name) === "[object Object]") {
          const { user_id, user_name } = _user_name;
          values.user_id = user_id;
          values.user_name = user_name;
        } else {
          values.user_name = _user_name;
        }

        // 处理兼职时间，拆分为 part_start_time 和 part_end_time
        const part_time = values.part_time; // 获取 RangePicker 的值
        const part_start_time = part_time
          ? moment(part_time[0]).format("YYYY.MM.DD")
          : null;
        const part_end_time = part_time
          ? moment(part_time[1]).format("YYYY.MM.DD")
          : null;

        // 删除原始的 part_time 字段
        delete values.part_time;
        values.part_start_time = part_start_time;
        values.part_end_time = part_end_time;

        const res = await addOrUpdatePartTimeCert({
          ...values,
          user_id: values.user_id,
        });

        if (res.data.code === 0) {
          setVisible(false);
          // 根据 modalType 动态调整提示信息
          message.success(`${modalType === "add" ? "添加" : "编辑"}成功`);
          loadData();
        } else {
          message.error(res.data.message);
        }
      }
    });
  };

  const modalHandleCancel = () => {
    setVisible(false);
  };

  const ModalForm = Form.create()(({ form }) => {
    const { getFieldDecorator } = form;
    const formItemLayout = {
      labelCol: {
        span: 8,
      },
      wrapperCol: {
        span: 12,
      },
    };

    return (
      <Form {...formItemLayout} labelAlign="right">
        {getFieldDecorator("part_time_id")(<input type="hidden" />)}
        {getFieldDecorator("user_id")(<input type="hidden" />)}
        <Form.Item label="姓名">
          <div className="user-box" style={{ display: "flex" }}>
            {getFieldDecorator("user_name", {
              rules: [
                { required: true, message: "输入后根据关键字匹配进行选择" },
              ],
            })(<PAutoComplete org_id={org_ids[0] || "1"} />)}
          </div>
        </Form.Item>
        <Form.Item label="兼任职务">
          {getFieldDecorator("part_time_position", {
            rules: [{ required: true, message: "请输入兼任职务" }],
          })(<Input placeholder="请输入" />)}
        </Form.Item>
        <Form.Item
          labelAlign="right"
          labelCol={{
            span: 8,
            style: {
              lineHeight: "normal",
              display: "flex",
              alignItems: "center",
            },
          }}
          label={
            <span>
              所兼职社会团体(企业）
              <br />
              业务主管部门：
            </span>
          }
          colon={false}
        >
          {getFieldDecorator("business_department", {
            rules: [
              {
                required: true,
                message: "请输入所兼职社会团体(企业）业务主管部门",
              },
            ],
          })(<Input placeholder="请输入" />)}
        </Form.Item>
        <Form.Item label="兼职时间">
          {getFieldDecorator("part_time", {
            rules: [{ required: true, message: "请输入兼职时间" }],
          })(
            <RangePicker
              disabledDate={(current) => {
                // 当前日期之后的日期不可选
                return current && current > moment().endOf("day");
              }}
            />
          )}
        </Form.Item>
        <Form.Item label="任职届数">
          {getFieldDecorator("term_count")(
            <InputNumber placeholder="请输入" min={0} />
          )}
        </Form.Item>
        <Form.Item label="是否兼法人代表">
          {getFieldDecorator("legal_representative", {
            rules: [{ required: false, message: "请输入" }],
          })(
            <Radio.Group>
              <Radio value={1}>是</Radio>
              <Radio value={2}>否</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        <Form.Item label="是否报批">
          {getFieldDecorator("approved", {
            rules: [{ required: false, message: "请输入" }],
          })(
            <Radio.Group>
              <Radio value={1}>是</Radio>
              <Radio value={2}>否</Radio>
            </Radio.Group>
          )}
        </Form.Item>
        <Form.Item label="审批文号">
          {getFieldDecorator("approval_number", {
            rules: [{ required: false, message: "请输入" }],
          })(<Input placeholder="请输入" />)}
        </Form.Item>
      </Form>
    );
  });
  return (
    <div className="annual-report">
      <div className="org-tree-box">
        <OrgTree onChange={onChange} />
      </div>
      <div className="content-box">
        <div className="search-box">
          <Form layout="inline">
            <Form.Item label="姓名">
              {getFieldDecorator("name")(
                <Input style={{ width: "120px" }} placeholder="请输入" />
              )}
            </Form.Item>
            <Form.Item label="兼任职务">
              {getFieldDecorator("part_time_position")(
                <Input style={{ width: "120px" }} placeholder="请输入" />
              )}
            </Form.Item>
            <Form.Item>
              <Button type="primary" icon="search" onClick={onSearch}>
                查询
              </Button>
              <Button icon="redo" className="reset-btn" onClick={onReset}>
                重置
              </Button>
              <Button
                onClick={onExport}
                loading={exportLoading}
                className="reset-btn"
              >
                导出
              </Button>
            </Form.Item>
          </Form>
        </div>
        <div className="add-box">
          <Button onClick={onAdd} type="primary" icon="plus">
            添加
          </Button>
          <Button onClick={onInputFile} className="input-file">
            导入
          </Button>
        </div>
        <Table
          bordered
          loading={loading}
          columns={columns}
          dataSource={dataSource}
          pagination={paginationProps}
          rowKey="index"
        />
      </div>
      <Modal
        title={(modalType === "add" ? "新增" : "编辑") + "兼职情况"}
        visible={visible}
        onOk={modalHandleOk}
        onCancel={modalHandleCancel}
        destroyOnClose
      >
        <ModalForm
          ref={(ref) => {
            modalFormRef.current = ref;
          }}
        />
      </Modal>
    </div>
  );
}

export default Form.create()(index);
