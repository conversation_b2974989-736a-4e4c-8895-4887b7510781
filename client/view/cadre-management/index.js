import {
  Button,
  DatePicker,
  Form,
  Icon,
  Input,
  message,
  Modal,
  Pagination,
  Select,
  Table,
  Tabs,
  Tree
} from "antd";
import {
  customizeExportRoster,
  deleteUser,
  getOrgUserList,
  queryByCode,
  queryCadreList,
  queryOrgType,
  transferCadre,
  updateLeaderSeq,
} from "client/apis/cadre-portrait";
import OrgTree from "client/components/org-tree";
import DateSingle from "components/date-single/DateSingle";
import { connect } from "dva";
import update from "immutability-helper";
import PropTypes from "prop-types";
import React, { Component, createRef } from "react";
import { DndProvider, DragSource, DropTarget } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import ChangePosition from "./components/ChangePosition";
import DownloadTemplateCheck from "./components/DownloadTemplateCheck";
import ImportModal from "./components/ImportModal";
import "./index.less";

const { MonthPicker, RangePicker, WeekPicker } = DatePicker;
const { TreeNode } = Tree;
const { TabPane } = Tabs;
let TREE_PROPS = null;

let dragingIndex = -1;

class BodyRow extends React.Component {
  render() {
    const {
      isOver,
      connectDragSource,
      connectDropTarget,
      moveRow,
      ...restProps
    } = this.props;
    const style = {
      ...restProps.style,
      cursor: restProps.allowDrag ? "move" : "auto",
    };

    let { className } = restProps;
    if (isOver) {
      if (restProps.index > dragingIndex) {
        className += " drop-over-downward";
      }
      if (restProps.index < dragingIndex) {
        className += " drop-over-upward";
      }
    }
    if (restProps)
      return restProps.allowDrag ? (
        connectDragSource(
          connectDropTarget(
            <tr {...restProps} className={className} style={style} />
          )
        )
      ) : (
        <tr {...restProps} className={className} />
      );
  }
}

const rowSource = {
  beginDrag(props) {
    dragingIndex = props.index;
    return {
      index: props.index,
    };
  },
};

const rowTarget = {
  drop(props, monitor) {
    const dragIndex = monitor.getItem().index;
    const hoverIndex = props.index;

    // Don't replace items with themselves
    if (dragIndex === hoverIndex) {
      return;
    }

    // Time to actually perform the action
    props.moveRow(dragIndex, hoverIndex);

    // Note: we're mutating the monitor item here!
    // Generally it's better to avoid mutations,
    // but it's good here for the sake of performance
    // to avoid expensive index searches.
    monitor.getItem().index = hoverIndex;
  },
};

const DragableBodyRow = DropTarget("row", rowTarget, (connect, monitor) => ({
  connectDropTarget: connect.dropTarget(),
  isOver: monitor.isOver(),
}))(
  DragSource("row", rowSource, (connect) => ({
    connectDragSource: connect.dragSource(),
  }))(BodyRow)
);

class LeaderGroup extends Component {
  constructor(props) {
    super(props);
    const currentUserInfo =
      typeof window !== "undefined"
        ? JSON.parse(window.sessionStorage.getItem("userInfo"))
        : {};
    const currentOid =
      typeof window !== "undefined"
        ? window.sessionStorage.getItem("_oid")
        : "";

    this.state = {
      eduOption: [], //学位
      tabs: [],
      userTypeList: [],
      modalform: {},
      deleteVisible: false,
      visible: false,
      category: [],
      org_unit_name: "",
      has_leader: "",
      current_page: 1,
      selectKey: currentOid,
      expandKeys: [],
      autoExpandParent: true,
      selectName: currentUserInfo.name || "",
      activeTabKey: 0,
      isTabSwitch: false,
      // 新增
      org_id: JSON.parse(sessionStorage.getItem("userInfo")).oid,
      importVisible: false,
      downRosterVisible: false,
      downFields: [],
      selectedFields: [],
      user_id: undefined,
      user_name: undefined,
      user_phone: "",
      user_label: "",

      page_size: 10,
      pageNum: 0,
      total: 0,
      dataSource: [],
      link: null,
      orgTypeList: [],
      currentSelect: {},
      orgType: 1, //1-班子，2-组织
      allowDrag: undefined, // 是否可以拖拽排序
      allowPagination: true, // 是否需要分页
      has_divided: "1",
      user_type: "",
      query_type: 2, // 查询层级：1-当前节点，2-当前节点及以下
    };
    this.changePage = this.changePage.bind(this);
    this.onSearch = this.onSearch.bind(this);
    this.onReset = this.onReset.bind(this);
    this.getOrgTypeList = this.getOrgTypeList.bind(this);
    this.onImportModalCancel = this.onImportModalCancel.bind(this);
    this.onImprotModalOk = this.onImprotModalOk.bind(this);
    this.onDownModalOk = this.onDownModalOk.bind(this);
    this.handleSelectChange = this.handleSelectChange.bind(this);
    this.onDownModalOkCancel = this.onDownModalOkCancel.bind(this);
    this.treeRef = createRef();
    this.moveRow = this.moveRow.bind(this);
    this.updateLeaderSeq = this.updateLeaderSeq.bind(this);
    this.handleListCategoryChange = this.handleListCategoryChange.bind(this);
    this.handleQueryTypeChange = this.handleQueryTypeChange.bind(this);
    this.components = {
      body: {
        row: DragableBodyRow,
      },
    };
  }
  componentDidMount() {
    this.getOrgTypeList();

    this.getOrgList();
    this.initCode(2001, "tabs");
    this.initCode(100301, "eduOption");
    this.initCode(97100, "userTypeList");
    // const currentOid =
    //   typeof window !== "undefined"
    //     ? window.sessionStorage.getItem("_oid")
    //     : "";

    // let orgId = null;

    if (TREE_PROPS) {
      this.setState(
        {
          org_id: TREE_PROPS.currentKey,
        },
        () => {
          this.getCadreList();
        }
      );
      // 灭霸写的，打响指优化！！
      this.treeRef.current.setTreeData(TREE_PROPS);

      setTimeout(() => {
        this.treeRef.current.scrollPos(String(TREE_PROPS.currentKey));

        TREE_PROPS = {};
      }, 500);
    } else {
      this.getCadreList();
    }

    let newpage = sessionStorage.getItem("newpage");
    if (newpage) {
      this.setState({
        current_page: Number(newpage),
      });
      sessionStorage.removeItem("newpage");
    }
  }
  async getOrgList() {
    const params = {
      name: this.state.user_name,
      phone: this.state.user_phone,
      user_tag: this.state.user_label,
      org_id: this.state.org_id,
      page: this.state.current_page,
      page_size: this.state.page_size,
    };
    const { data } = await getOrgUserList(params);

    if (data.code == 0) {
      this.setState({
        // dataSource: data.data,
        pageNum: data.pageNum,
        total: data.total,
      });
    } else {
      message.error(data.message);
    }
  }
  getCadreList(page, org_id, category) {
    const _category = category || this.state.category;

    const params = {
      page: page || this.state.current_page,
      org_id: org_id || this.state.org_id,
      category: _category.flat(),
      name: this.state.name,
      page_size: this.state.orgType === 2 ? 999 : 10,
      user_type: this.state.user_type,
      query_type: this.state.query_type,
    };

    queryCadreList(params).then((res) => {
      if (res.data.code === 0) {
        const data = res.data.data;
        this.setState({
          dataSource: data.content,
          totalElements: data.totalElements,

          current_page: page || this.state.current_page,
          size: data.size,
        });
      } else {
        message.error(res.data.message);
      }
    });
  }
  changePage(page) {
    this.getCadreList(page);
  }

  onAdd() {
    console.log(this.state.org_id);
    this.props.history.push(
      `/cadre-information-maintenance?org_id=${this.state.org_id}`
    );
  }
  initCode(code, values) {
    queryByCode({ code }).then((res) => {
      if (res.data.code === 0) {
        const data = res.data.data;

        this.setState({
          // tabs: data,
          [values]: data,
        });
      } else {
        message.error(res.data.message);
      }
    });
  }
  changeTabs(key) {
    this.setState({
      category: [key],
    });
  }
  onEditor({ user_id, org_id }) {
    TREE_PROPS = this.treeRef.current.getTreeData();

    this.props.history.push(
      `/cadre-information-maintenance?user_id=${user_id}&org_id=${this.state.org_id}&sel_org_id=${org_id}`
    );
  }
  onDetail({ user_id, org_id }) {
    TREE_PROPS = this.treeRef.current.getTreeData();

    this.props.history.push(
      `/cadre-information-maintenance?user_id=${user_id}&is_detail=1&sel_org_id=${org_id}`
    );
  }
  selectTree(key) {
    if (!key.length) return;

    const { expandKeys } = this.state;

    const hasIndex = expandKeys.findIndex((item) => {
      return item == key[0];
    });

    if (hasIndex !== -1) {
      expandKeys.push(key[0]);
    }

    this.setState(
      {
        org_id: key[0],
        current_page: 1,
        expandKeys: [...expandKeys],
      },
      () => {
        this.queryOrgType(key[0]);
      }
    );
  }
  // 组织类型获取
  getOrgTypeList(orgId) {
    const { dispatch } = this.props;
    const { selectKey } = this.state;
    dispatch({
      type: "leaderGroup/getTreeTypeList",
      payload: {
        org_id: orgId || selectKey,
      },
    }).then(() => {
      this.loadTreeList(orgId || selectKey);
    });
  }
  onSearch() {
    const { name, category, user_type } = this.state;

    // 如果没有查询条件（name、category、user_type 都为空），则重置页码为1
    if (!name && (!category || category.length === 0) && !user_type) {
      this.setState(
        {
          current_page: 1,
        },
        () => {
          this.getCadreList();
        }
      );
    } else {
      // 如果有查询条件，保持当前页码
      this.getCadreList();
    }
  }
  onReset() {
    this.setState(
      {
        name: undefined,
        category: [],
        user_type: "",
        query_type: 2, // 重置为默认值
        current_page: 1,
        allowDrag: this.state.orgType === 2 && false, // 重置拖拽状态，默认query_type=2时不允许拖拽
      },
      () => {
        this.getCadreList();
      }
    );
  }
  onImprotModalOk() {
    this.getCadreList();
  }

  onImportModalCancel() {
    this.setState({
      importVisible: false,
    });
  }

  //#region ====================== 下载名册 ======================
  handleSelectChange(selectedFields) {
    this.setState({
      selectedFields,
    });
  }
  async onDownModalOk() {
    const { selectedFields } = this.state;
    if (selectedFields.length === 0) {
      message.warning("请至少选择一个字段");
      return;
    }
    const _category = this.state.category;
    const params = {
      // page: page || this.state.page,
      org_id: this.state.org_id,
      cadre_category: _category.flat(),
      name: this.state.name,
      // page_size: this.state.orgType === 2 ? 999 : 10,
      user_type: this.state.user_type,
    };
    // 调用 customizeExportRoster 方法获取下载数据
    const res = await customizeExportRoster({
      need_export_fieids: selectedFields,
      leader_query_vo: params,
    });
    // 关闭弹窗
    this.setState({
      downRosterVisible: false,
    });
  }
  onDownModalOkCancel() {
    this.setState({
      downRosterVisible: false,
    });
  }
  //#endregion
  onTransferCadre() { }
  // 左边树 滚动到指定位置
  expandTree(keys, params) {
    const { expanded, node } = params;
    const {
      dataRef: { org_id },
    } = node.props;
    this.setState(
      {
        expandKeys: keys,
        autoExpandParent: false,
        isTabSwitch: false,
        org_id: org_id,
      },
      () => {
        expanded && this.loadTreeList(org_id);

        this.getCadreList();
      }
    );
  }

  tabChange(key) {
    this.setState(
      {
        activeTabKey: key,
        isTabSwitch: true,
        autoExpandParent: false,
        expandKeys: [],
        selectKey: this.state.selectKey,
        current_page: 1,
      },
      () => {
        this.loadTreeList(this.state.selectKey);
        this.loadOrgUnit();
      }
    );
  }
  //切换树
  loadOrgUnit() {
    const { selectKey } = this.state;
    this.setState(
      {
        org_id: selectKey,
      },
      () => {
        this.getOrgList();
      }
    );
  }
  //组织树
  loadTreeList(org_id) {
    const { dispatch, leaderGroup } = this.props;
    const { activeTabKey, isTabSwitch } = this.state;
    const { treeTypeList } = leaderGroup;
    const { tree_type, org_type } = treeTypeList[activeTabKey];
    const payload = {
      tree_type,
      org_type,
      org_id,
      isTabSwitch,
    };
    if (parseInt(tree_type) === 1) {
      delete payload.org_type;
    }
    dispatch({
      type: "leaderGroup/getOrgTree",
      payload,
    });
  }
  // 模糊查询组织列表
  searchOrgList(value) {
    console.log(this.state.selectKey, "this.state.selectKey");
    const { dispatch } = this.props;
    if (!value) {
      this.setState(
        {
          selectKey: JSON.parse(sessionStorage.getItem("userInfo")).oid,
        },
        () => {
          dispatch({
            type: "leaderGroup/findOrgByName",
            payload: {
              org_id: this.state.selectKey,
              org_name: value,
              tree_type: 2,
            },
          });
        }
      );
    } else {
      dispatch({
        type: "leaderGroup/findOrgByName",
        payload: {
          org_id: this.state.selectKey,
          org_name: value,
          tree_type: 2,
        },
      });
    }
  }
  // 点击组织树
  selectTreeOrg(value, name) {
    this.setState(
      {
        selectKey: value,
        selectName: name,
        current_page: 1,
        org_id: value,
      },
      () => {
        this.getOrgList();
        this.loadOrgUnit();
      }
    );
  }
  handleListCategoryChange(val) {
    this.setState({
      user_type: val,
    });
  }

  // 处理查询模式切换
  handleQueryTypeChange(query_type) {
    this.setState(
      {
        query_type,
        allowDrag: this.state.orgType === 2 && query_type === 1,
        current_page: 1, // 切换模式时重置到第一页
      },
      () => {
        this.getCadreList();
      }
    );
  }
  queryOrgType(org_id) {
    queryOrgType({
      org_id,
    }).then(({ data: res }) => {
      if (res.code === 0) {
        this.setState(
          {
            orgType: res.data.org_type,
            allowDrag: res.data.org_type === 2 && this.state.query_type === 1,
            allowPagination: res.data.org_type === 1,
          },
          () => {
            this.getCadreList();
          }
        );
      }
    });
  }
  // 左边树查询
  selectOrg(value, option) {
    const { key } = option;
    const { dispatch, leaderGroup } = this.props;
    const { activeTabKey } = this.state;
    const { treeTypeList } = leaderGroup;
    const { tree_type, org_type } = treeTypeList[activeTabKey];
    const payload = {
      root_org_id: this.state.selectKey,
      org_id: key,
      org_type,
      load_root: 1,
      tree_type,
    };
    if (parseInt(tree_type) === 1) {
      delete payload.org_type;
    }
    if (key == this.state.selectKey) {
      return;
    }
    dispatch({
      type: "leaderGroup/getAllOrgs",
      payload,
    }).then(() => {
      this.setState(
        {
          selectKey: key,
          expandKeys: [key],
          autoExpandParent: true,
          selectName: value,
          current_page: 1,
          org_id: key,
        },
        () => {
          this.getOrgList();
          this.loadOrgUnit();
        }
      );
      this._scrollPos(key);
    });
  }

  //树
  renderTreeNodes(data) {
    return data.map((item) => {
      const { child_org_num, org_id, name, short_name } = item;
      const { selectKey } = this.state;
      if (item.children) {
        return (
          <TreeNode
            isLeaf={child_org_num === 0}
            title={
              <div
                ref={org_id}
                title={name || short_name}
                className={
                  parseInt(selectKey) === parseInt(org_id) ? "active" : ""
                }
                onClick={this.selectTreeOrg.bind(this, org_id, name)}
              >
                {short_name || name}
              </div>
            }
            key={org_id}
            dataRef={item}
          >
            {this.renderTreeNodes(item.children)}
          </TreeNode>
        );
      }
      return (
        <TreeNode
          isLeaf={child_org_num === 0}
          title={
            <div
              ref={org_id}
              onClick={this.selectTreeOrg.bind(this, org_id, name)}
            >
              {short_name || name}
            </div>
          }
          key={org_id}
          dataRef={item}
        />
      );
    });
  }
  handleOk() {
    const { form } = this.props;

    const { getFieldsValue, validateFields } = form;
    validateFields((err) => {
      if (!err) {
        const { job, transfer_time, remark, retain_old, category } =
          getFieldsValue();

        if (!job || !job.length) return message.warn("未选择职务");

        if (!transfer_time) return message.warn("请选择调整时间");

        const { job_id, job_name, org_id: _org_id } = job[0];
        const { user_id, org_id } = this.state.currentSelect;

        if (!job_name.trim()) {
          return message.warn("请填写职务");
        }

        const params = {
          job_id,
          user_id,
          org_id: _org_id || org_id || this.state.selectKey,
          remark,
          job_name,
          category,
          retain_old,
          transfer_time,
        };

        // if (transfer_time) {
        //   params.transfer_time = transfer_time.format("YYYY.MM.DD");
        // }

        transferCadre(params).then((res) => {
          if (res.data.code === 0) {
            message.success("调整成功");

            this.setState(
              {
                visible: false,
                currentSelect: {},
              },
              () => {
                this.getCadreList();
              }
            );
          } else {
            message.error(res.data.message);
          }
        });
      }
    });
  }
  handleCancel() {
    this.setState({
      visible: !this.state.visible,
    });
  }
  cancel(e) {
    message.error("Click on No");
  }
  confirm(params) { }

  handleDeleteOk() {
    const { currentSelect } = this.state;
    const { getFieldsValue } = this.props.form;

    const params = getFieldsValue();
    if (!params.delete_reason) {
      return message.error("请选择删除原因");
    }

    deleteUser({
      user_id: currentSelect.user_id,
      delete_reason: params.delete_reason,
    }).then((res) => {
      if (res.data.code === 0) {
        message.success("删除成功");

        this.setState(
          {
            deleteVisible: !this.state.deleteVisible,
          },
          () => {
            this.getCadreList();
          }
        );
      } else {
        message.error(res.data.message);
      }
    });
  }
  handleDeleteCancel() {
    this.setState({
      deleteVisible: !this.state.deleteVisible,
      currentSelect: {},
    });
  }
  async updateLeaderSeq(leader_team_ids) {
    const { data: res } = await updateLeaderSeq(leader_team_ids);
    if (res.code === 0) {
      this.getCadreList();
    } else {
      message.error(res.message);
    }
  }

  moveRow(dragIndex, hoverIndex) {
    const { dataSource } = this.state;
    const dragRow = dataSource[dragIndex];

    this.setState(
      update(this.state, {
        dataSource: {
          $splice: [
            [dragIndex, 1],
            [hoverIndex, 0, dragRow],
          ],
        },
      }),
      () => {
        this.updateLeaderSeq(
          this.state.dataSource.map((item) => item.leader_team_id)
        );
      }
    );
  }

  render() {
    const {
      tabs,
      userTypeList,
      visible,
      category,
      list_category,
      autoExpandParent,
      expandKeys,
      deleteVisible,
    } = this.state;

    const { dispatch, form, leaderGroup } = this.props;
    const { getFieldDecorator, setFieldsValue } = form;
    const totalPage = Math.ceil(leaderGroup.totalCount / 10);
    const _expandKeys =
      leaderGroup &&
        leaderGroup.treeList &&
        leaderGroup.treeList.length &&
        leaderGroup.treeList[0].children.length
        ? leaderGroup.treeList[0].org_id
        : "";
    const columns = [
      {
        title: "姓名",
        dataIndex: "user_name",
        key: "user_name",
        width: "6%",
        align: "center",
      },
      {
        title: "现任职务",
        dataIndex: "current_job",
        key: "current_job",
        // width: 150,
        align: "center",
        render(_) {
          return <div style={{ width: "100%", textAlign: "left" }}>{_}</div>;
        },
      },
      {
        title: "所在单位",
        dataIndex: "org_name",
        key: "org_name",
        // width: 50,
        align: "center",
      },
      {
        title: "干部类别",
        dataIndex: "category",
        key: "category",
        width: "10%",
        align: "center",
      },
      {
        title: "性别",
        dataIndex: "gender",
        key: "gender",
        width: "5%",
        align: "center",
      },
      {
        title: "出生年月",
        dataIndex: "birthday",
        key: "birthday",
        width: "7%",
        align: "center",
      },
      {
        title: "操作",
        dataIndex: "handle",
        key: "handle",
        align: "center",
        width: "15%",
        render: (_, record) => {
          return (
            <div className="handle">
              <a
                onClick={() => {
                  this.onDetail(record);
                  sessionStorage.setItem("newpage", this.state.current_page);
                }}
              >
                详情
              </a>
              <a
                onClick={() => {
                  this.onEditor(record);
                  sessionStorage.setItem("newpage", this.state.current_page);
                }}
              >
                编辑
              </a>
              <a
                onClick={() => {
                  this.setState({
                    visible: !visible,
                    currentSelect: record,
                  });
                }}
              >
                干部调整
              </a>
              <a
                onClick={() => {
                  this.setState({
                    currentSelect: record,
                    deleteVisible: true,
                  });
                }}
              >
                删除
              </a>
            </div>
          );
        },
      },
    ];
    const data = [
      {
        key: "1",
        name: "John Brown",
        post: "县长",
        sort: "县管正职",
        gender: "男",
        time: "1995-12-17",
      },
    ];
    const formItemLayout = {
      labelCol: {
        span: 5,
      },
      wrapperCol: {
        span: 16,
      },
    };
    const { current_page } = this.state;
    return (
      <div className="cadre-management">
        <div className="left">
          {/* <AutoComplete
            className="searchOrg"
            placeholder="请输入组织名称"
            allowClear
            optionLabelProp="value"
            onSearch={this.searchOrgList.bind(this)}
            onSelect={this.selectOrg.bind(this)}
          >
            {leaderGroup &&
              leaderGroup.orgList &&
              leaderGroup.orgList.map((item) => {
                return (
                  <AutoComplete.Option
                    onClick={(e) => this.selectTree([item.value])}
                    key={item.org_id}
                    value={item.org_name}
                    text={item.org_name}
                  >
                    {item.org_name}
                  </AutoComplete.Option>
                );
              })}
          </AutoComplete>
          <div className="orgContainer" ref="orgContainer">
            <Tree
              blockNode={true}
              autoExpandParent={autoExpandParent}
              onExpand={this.expandTree.bind(this)}
              expandedKeys={
                expandKeys.length ? expandKeys : [_expandKeys.toString()]
              }
              onSelect={this.selectTree.bind(this)}
            >
              {leaderGroup.treeList
                ? this.renderTreeNodes(leaderGroup.treeList)
                : null}
            </Tree>
          </div> */}
          <OrgTree
            onChange={this.selectTree.bind(this)}
            ref={(instance) => {
              if (instance) {
                this.treeRef.current = instance.wrappedInstance;
              }
            }}
          />
        </div>
        <div className="right">
          <div className="cadre-header">
            <div className="header-search">
              <div className="header-item">
                <span className="h-i-name">姓名：</span>
                <Input
                  placeholder="请输入"
                  style={{ width: "200px" }}
                  value={this.state.name}
                  onChange={(e) => {
                    this.setState({
                      name: e.target.value,
                    });
                  }}
                />
              </div>

              {/*  className="search-span" */}
              <div className="header-item">
                <span className="h-i-name">干部类别：</span>
                <Select
                  placeholder="请选择"
                  value={category[0] || []}
                  mode="multiple"
                  style={{ width: "200px" }}
                  onChange={(op_key) => {
                    this.changeTabs(op_key);
                  }}
                // defaultValue={"all"}
                >
                  {/* <Select.Option value={"all"}>全部</Select.Option> */}
                  {tabs &&
                    tabs.length > 0 &&
                    tabs.map((item) => {
                      return (
                        <Select.Option value={item.op_key}>
                          {item.op_value}
                        </Select.Option>
                      );
                    })}
                </Select>
                {/* 
                <span
                  className={!category ? "search-active" : "search-span"}
                  onClick={() => {
                    this.changeTabs.bind(this)();
                  }}
                ></span> */}
              </div>
              <div className="header-item">
                <span className="h-i-name">干部分类：</span>
                <Select
                  value={this.state.user_type}
                  style={{ width: "200px" }}
                  placeholder="请选择"
                  onChange={this.handleListCategoryChange}
                >
                  {userTypeList &&
                    userTypeList.length > 0 &&
                    userTypeList.map((item) => {
                      return (
                        <Select.Option value={item.op_key}>
                          {item.op_value}
                        </Select.Option>
                      );
                    })}
                </Select>
              </div>
              <div className="header-item">
                <span className="h-i-name">查询模式：</span>
                <Select
                  value={this.state.query_type}
                  style={{ width: "200px" }}
                  placeholder="请选择查询模式"
                  onChange={this.handleQueryTypeChange}
                >
                  <Select.Option value={1}>当前节点</Select.Option>
                  <Select.Option value={2}>当前节点及以下</Select.Option>
                </Select>
              </div>
              {/* <div className="header-item">
                <span className="h-i-name">学位：</span>
                <Select
                  value={this.state.eduOption}
                  style={{ width: "200px" }}
                  placeholder="请选择"
                  onChange={this.handleListCategoryChange}
                >
                  <Option value="1">班子成员</Option>
                  <Option value="2">其他干部</Option>
                </Select>
              </div>
              <div className="header-item">
                <span className="h-i-name">在职教育院校：</span>
                <Input
                  placeholder="请输入"
                  style={{ width: "200px" }}
                  value={this.state.name}
                  onChange={(e) => {
                    this.setState({
                      name: e.target.value,
                    });
                  }}
                />
              </div>
              <div className="header-item">
                <span className="h-i-name">专业：</span>
                <Input
                  placeholder="请输入"
                  style={{ width: "200px" }}
                  value={this.state.name}
                  onChange={(e) => {
                    this.setState({
                      name: e.target.value,
                    });
                  }}
                />
              </div> */}

              <div className="search-btn">
                <div onClick={this.onSearch}>查询</div>
                <div className="reset" onClick={this.onReset}>
                  重置
                </div>
              </div>
            </div>
            <div className="button-box">
              <div
                className="header-btn"
                onClick={() => {
                  this.onAdd();
                }}
              >
                <Icon type="plus" />
                添加
              </div>
              <Button
                type="primary"
                ghost
                onClick={() => {
                  this.setState({
                    importVisible: true,
                  });
                }}
              >
                审批表导入
              </Button>
              <Button
                type="primary"
                onClick={() => {
                  this.setState({
                    downRosterVisible: true,
                  });
                }}
              >
                下载名册
              </Button>
            </div>
          </div>
          <div className="cadre-content">
            <DndProvider backend={HTML5Backend}>
              <Table
                bordered
                columns={columns}
                dataSource={this.state.dataSource}
                pagination={
                  // this.state.allowPagination
                  //   ? {
                  //     total: this.state.totalElements,
                  //     pageSize: 10,
                  //     current: this.state.page,
                  //     onChange: this.changePage,
                  //   }
                  //   :
                  false
                }
                components={this.components}
                onRow={(record, index) => ({
                  index,
                  moveRow: this.moveRow,
                  allowDrag: this.state.allowDrag,
                })}
              />
              {this.state.allowPagination && (
                <Pagination
                  current={current_page}
                  style={{ marginTop: "20px" }}
                  total={this.state.totalElements}
                  pageSize={this.state.page_size}
                  defaultCurrent={current_page}
                  onChange={(e) => this.changePage(e)}
                />
              )}
            </DndProvider>
          </div>
        </div>

        <Modal
          title="干部调整"
          visible={visible}
          onOk={this.handleOk.bind(this)}
          onCancel={this.handleCancel.bind(this)}
          width={"850px"}
          destroyOnClose
          className="dr-modal-box"
        >
          <Form {...formItemLayout} ref={(ref) => (this.modalform = ref)}>
            <Form.Item label="现任职务">
              <div className="custom-box">
                <div className="job-box">
                  <span>{this.state.currentSelect.current_job}</span>
                </div>
                {/* <div className="job-type">

                </div> */}
              </div>
            </Form.Item>
            <Form.Item label="干部类别">
              {getFieldDecorator("category", {
                rules: [{ required: true, message: "请选择干部类别" }],
              })(
                <Select
                  placeholder="请选择或输入干部类别"
                  showSearch
                  filterOption={(input, option) =>
                    option.props.children
                      .toLowerCase()
                      .indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {this.state.tabs.map((item) => (
                    <Select.Option key={item.op_key} value={item.op_key}>
                      {item.op_value}
                    </Select.Option>
                  ))}
                </Select>
              )}
            </Form.Item>
            <Form.Item label="调整时间">
              {getFieldDecorator("transfer_time", {
                rules: [{ required: true, message: "请选择时间" }],
              })(<DateSingle placeholder="请选择" type="month" />)}
            </Form.Item>
            <Form.Item label="调整职务" required>
              {getFieldDecorator("job", {
                rules: [{ required: true, message: "请选择调整职务" }],
              })(
                <ChangePosition
                  current_job={this.state.currentSelect}
                  form={form}
                  onCheckChange={(val) => {
                    setFieldsValue({ retain_old: val });
                  }}
                />
              )}
            </Form.Item>
            {getFieldDecorator("retain_old")(<Input hidden />)}
            <Form.Item label="调整原因">
              {getFieldDecorator(
                "remark",
                {}
              )(<Input placeholder="请输入" style={{ width: "300px" }} />)}
            </Form.Item>
          </Form>
        </Modal>

        <Modal
          title="提示"
          visible={deleteVisible}
          onOk={this.handleDeleteOk.bind(this)}
          // confirmLoading={confirmLoading}
          onCancel={this.handleDeleteCancel.bind(this)}
          destroyOnClose
        >
          {getFieldDecorator(
            "delete_reason",
            {}
          )(
            <Select placeholder="请选择" style={{ width: 220 }}>
              <Select.Option value={"改非"}>改非</Select.Option>
              <Select.Option value={"违纪"}>违纪</Select.Option>
              <Select.Option value={"死亡"}>死亡</Select.Option>
              <Select.Option value={"调离"}>调离</Select.Option>
              <Select.Option value={"其他"}>其他</Select.Option>
            </Select>
          )}
        </Modal>
        <ImportModal
          visible={this.state.importVisible}
          org_id={this.state.org_id}
          onOk={this.onImprotModalOk}
          onCancel={this.onImportModalCancel}
        />
        <DownloadTemplateCheck
          visible={this.state.downRosterVisible}
          downFields={columns}
          selectedFields={this.state.selectedFields}
          onOk={this.onDownModalOk}
          onCancel={this.onDownModalOkCancel}
          onSelectChange={this.handleSelectChange}
        />
      </div>
    );
  }
}

LeaderGroup.propTypes = {
  props: PropTypes.object,
  leaderGroup: PropTypes.object,
};
LeaderGroup.defaultProps = {
  props: {},
  leaderGroup: {},
};

const mapStateToProps = ({ leaderGroup }) => ({ leaderGroup });

export default connect(mapStateToProps)(Form.create()(LeaderGroup));
