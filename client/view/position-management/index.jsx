import {
  Button,
  Form,
  Input,
  InputNumber,
  Modal,
  Popconfirm,
  Select,
  Table,
  message,
} from "antd";
import { getPositionList, queryByCode } from "client/apis/cadre-portrait";
import OrgTree from "client/components/org-tree";
import { useEffect, useRef, useState } from "react";
import "./index.less";
const { Option } = Select;
const FormItem = Form.Item;
function PositionManagement({ form, history }) {
  const { getFieldDecorator, getFieldsValue, resetFields, setFieldsValue } =
    form;
  // 初始空 position 状态
  const initialPosition = {
    name: "",
    type: undefined,
    short_name: "",
    part_job_name: "",
    part_job_short_name: "",
    title: "",
    category: undefined,
    num: undefined,
    typeName: "", // 用于显示分类名称
  };

  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [pageState, setPageState] = useState({
    org_name: "",
    org_id: 1,
  });
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [org_name, setOrgName] = useState("");
  const [org_ids, setOrgIds] = useState([1]);
  const [exportLoading, setExportLoading] = useState(false);
  const [position, setPosition] = useState(initialPosition);
  const [buttonStatus, setButtonStatus] = useState({
    save: false,
    modalSave: false,
  });
  const [codeMap, setCodeMap] = useState({
    categoryOption: [],
    classificationOption: [], //分类
  });
  const [visible, setVisible] = useState(false);
  const orgRef = useRef(null);
  useEffect(() => {
    initCodeMap(96160, "categoryOption");
    initCodeMap(96400, "classificationOption");
    initData(org_ids[0]);
  }, []);

  const initCodeMap = async (code, key) => {
    const res = await queryByCode({
      code,
    });
    if (res.data.code === 0) {
      codeMap[key] = res.data.data;
      setCodeMap({
        ...codeMap,
      });
    }
  };
  const loadData = async ({ page: _page = 1, org_id = org_ids[0] } = {}) => {
    const fields = getFieldsValue();
    const params = {
      org_id,
      name: fields.name,
      category: fields.category,
    };
    try {
      const res = await getPositionList(params);
      if (res.data.code === 0) {
        const pmsJobList = res.data.data.flatMap((item) => item.pms_job_list);
        setData(pmsJobList);
        setDataSource(processedData);
        setPagination((prev) => ({
          ...prev,
          total: total,
        }));
      } else {
        message.error(res.data.message);
        // 可选：在错误情况下重置数据源和分页信息
        setDataSource([]);
        setPagination((prev) => ({
          ...prev,
          total: 0,
        }));
      }
    } catch (error) {
      message.error("请求失败，请稍后再试");
    } finally {
      setLoading(false);
    }
  };
  const onChange = (orgs, org) => {
    setOrgIds(() => orgs);
    setOrgName(org.name);
    loadData({
      page: 1,
      org_id: orgs[0],
    });
  };

  const onAdd = () => {
    setVisible(true);
    setModalType("add");
  };
  //职务管理导入
  const onInputFile = () => {
    return;
    history.push(`/import-page?type=21`);
  };

  const onDel = async (record) => {
    try {
      const res = await deletePosition({
        pms_job_id: record.pms_job_id,
      });

      if (res.data.code === 0) {
        // 删除成功
        message.success("删除成功");
        loadData(); // 重新加载数据
      } else {
        // 删除失败
        message.error(res.data.message || "删除失败，请稍后再试");
      }
    } catch (error) {
      // 网络或其他错误
      message.error("删除失败，请稍后再试");
    }
  };

  const onEditor = (record) => {
    setVisible(true);
    setModalType("edit");
    queueMicrotask(() => {
      const _ = { ...record };
      // 查找分类名称
      const classificationOption =
        codeMap.classificationOption &&
        codeMap.classificationOption.find((item) => item.op_key == _.type);
      const typeName = classificationOption
        ? classificationOption.op_value
        : "";

      // 设置 position 的值
      setPosition({
        ...record,
        typeName: typeName, // 用于显示分类名称
      });

      // 设置表单值
      modalFormRef.current.setFieldsValue(_);
    });
  };

  const columns = [
    {
      title: "职务全称",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "职务简称",
      dataIndex: "short_name",
      key: "short_name",
    },
    {
      title: "兼职全称",
      dataIndex: "part_job_name",
      key: "part_job_name",
    },
    {
      title: "兼职简称",
      dataIndex: "part_job_short_name",
      key: "part_job_short_name",
    },
    {
      title: "发文抬头",
      dataIndex: "title",
      key: "title",
    },
    {
      title: "职务分类",
      dataIndex: "type",
      key: "type",
      render: (type) => {
        const option = codeMap.classificationOption.find(
          (item) => item.op_key == type
        );
        return option ? option.op_value : "";
      },
    },
    {
      title: "职务数量",
      dataIndex: "num",
      key: "num",
    },
    {
      title: "干部姓名",
      dataIndex: "cadreName",
      key: "cadreName",
    },
    {
      title: "操作",
      key: "operation",
      render: (_, record) => (
        <div>
          <Button type="link" onClick={() => handleEdit(record)}>
            编辑
          </Button>
          <Popconfirm
            title="确认删除？"
            onConfirm={() => handleDelete(record)}
            okText="是"
            cancelText="否"
          >
            <Button type="link" className="set_del">
              删除
            </Button>
          </Popconfirm>
        </div>
      ),
    },
  ];
  const paginationProps = {
    pageSize: 10,
    onChange: (page) => {
      setPage(page);
      loadData({ page });
    },
    current: page,
    total: pagination.total,
  };

  const onReset = () => {
    resetFields();
    onSearch();
  };

  const onSearch = () => {
    setDataSource([]); // 清空旧数据
    initData(pageState.org_id);
  };
  const onExport = async () => {
    // setExportLoading(true);
    // const params = getFieldsValue();
    // const res = await exportAnnualEval({
    //   ...params,
    //   org_id: pageState.org_id,
    // });
    // setExportLoading(false);
  };
  const handleModalOk = async () => {
    form.validateFields(async (err, values) => {
      if (err) return;
      setButtonStatus((state) => ({ ...state, modalSave: true }));
      try {
        const { page_status } = pageState;
        let params = {
          ...values,
          organization_id: org_ids[0], // 添加当前选中的组织ID
        };
        if (page_status === 2) {
          // 编辑操作，添加唯一标识
          params = {
            ...params,
            pms_job_id: position.pms_job_id, // 假设 position 是当前编辑的记录对象
          };
        }

        // 如果是编辑状态，将分类名称转换回数字键
        if (page_status === 2) {
          const classificationOption =
            codeMap.classificationOption &&
            codeMap.classificationOption.find(
              (item) => item.op_value === values.type
            );
          if (classificationOption) {
            params.type = classificationOption.op_key; // 转换为数字键
          }
        }

        const res = await addPosition(params);

        if (res.data.code === 0) {
          message.success(`${page_status === 1 ? "新增" : "编辑"}成功`);
          initData(org_ids[0]); // 使用当前选中的组织ID刷新数据
        } else {
          message.error(res.data.message);
        }
      } catch (error) {
        // 网络请求错误处理
        message.error("请求失败，请稍后重试");
      } finally {
        // 无论成功还是失败都关闭加载状态
        setButtonStatus((state) => ({ ...state, modalSave: false }));
        setVisible(false);
      }
    });
  };

  const modalHandleCancel = () => {
    setVisible(false);
  };

  const ModalForm = Form.create()(({ form }) => {
    const { getFieldDecorator } = form;
    const formItemLayout = {
      labelCol: {
        span: 8,
      },
      wrapperCol: {
        span: 12,
      },
    };

    return (
      <Form {...formItemLayout}>
        {getFieldDecorator("pms_job_id")(<input type="hidden" />)}
        <FormItem label="分类">
          {getFieldDecorator("type", {
            initialValue: position.typeName || "", // 如果是编辑状态，回显分类名称
          })(
            <Select
              disabled={pageState.page_status === 2} // 编辑状态下禁用
              placeholder="请选择分类"
            >
              {codeMap.classificationOption.map((item) => (
                <Option key={item.op_key} value={item.op_key}>
                  {item.op_value}
                </Option>
              ))}
            </Select>
          )}
        </FormItem>
        <FormItem label="职务全称">
          {getFieldDecorator("name", {
            initialValue: position.name,
          })(<Input placeholder="请输入" />)}
        </FormItem>
        <FormItem label="职务简称">
          {getFieldDecorator("short_name", {
            initialValue: position.short_name,
            rules: [{ required: true, message: "请填写职务简称" }],
          })(<Input placeholder="请输入" />)}
        </FormItem>
        <FormItem label="兼职全称">
          {getFieldDecorator("part_job_name", {
            initialValue: position.part_job_name,
          })(<Input placeholder="请输入" />)}
        </FormItem>
        <FormItem label="兼职简称">
          {getFieldDecorator("part_job_short_name", {
            initialValue: position.part_job_short_name,
          })(<Input placeholder="请输入" />)}
        </FormItem>
        <FormItem label="发文抬头">
          {getFieldDecorator("title", {
            initialValue: position.title,
          })(<Input placeholder="请输入" />)}
        </FormItem>
        <FormItem label="职务类别">
          {getFieldDecorator("category", {
            initialValue: position.category,
            rules: [{ required: true, message: "请选择职务类别" }],
          })(
            <Select>
              {codeMap.categoryOption.map((item) => (
                <Option key={item.op_key} value={item.op_key}>
                  {item.op_value}
                </Option>
              ))}
            </Select>
          )}
        </FormItem>
        <FormItem label="职务数量">
          {getFieldDecorator("num", {
            initialValue: position.num,
            rules: [{ required: true, message: "请填写职务数量" }],
          })(<InputNumber placeholder="请输入" />)}
        </FormItem>
      </Form>
    );
  });
  return (
    <div className="position-management">
      <div className="org-tree-box">
        <OrgTree onChange={onChange} ref={(ref) => (orgRef.current = ref)} />
      </div>
      <div className="content-box">
        <div className="search-box">
          <Form layout="inline">
            <Form.Item label="职务全称">
              {getFieldDecorator("name")(
                <Input
                  style={{ width: "200px" }}
                  placeholder="请输入"
                  allowClear
                />
              )}
            </Form.Item>

            <Form.Item label="职务类别">
              {getFieldDecorator("category")(
                <Select
                  style={{ minWidth: "200px" }}
                  placeholder="请选择"
                  allowClear
                >
                  {codeMap.categoryOption.map((item) => (
                    <Option key={item.op_key} value={item.op_key}>
                      {item.op_value}
                    </Option>
                  ))}
                </Select>
              )}
            </Form.Item>
            <Form.Item>
              <Button type="primary" icon="search" onClick={onSearch}>
                查询
              </Button>
              <Button icon="redo" className="reset-btn" onClick={onReset}>
                重置
              </Button>
              <Button
                onClick={onExport}
                loading={exportLoading}
                disabled={true}
                className="reset-btn"
              >
                导出
              </Button>
            </Form.Item>
          </Form>
        </div>
        <div className="add-box">
          <Button onClick={handleAdd} type="primary" icon="plus">
            添加职务
          </Button>
          <Button onClick={onInputFile} className="input-file" disabled={true}>
            导入
          </Button>
        </div>
        <Table
          bordered
          loading={loading}
          columns={columns}
          dataSource={data}
          rowKey="pms_job_id"
        />
      </div>
      <Modal
        title={(modalType === "add" ? "新增" : "编辑") + "兼职情况"}
        visible={visible}
        onOk={modalHandleOk}
        onCancel={modalHandleCancel}
        destroyOnClose
      >
        <ModalForm
          ref={(ref) => {
            modalFormRef.current = ref;
          }}
        />
      </Modal>
    </div>
  );
}

export default Form.create()(PositionManagement);
